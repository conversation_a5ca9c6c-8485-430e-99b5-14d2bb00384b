#!/bin/bash

set -eu

GREEN="\033[0;32m"
NC="\033[0m"
logger() {
  echo -e "${GREEN}$(date "+%Y/%m/%d %H:%M:%S") ecr-login: $1${NC}"
}

logger "Setting up Ruby"
export PATH="$HOME/.rbenv/bin:$PATH"
# Ignore .ruby-version and use the Ruby version that's installed in the Buildkite agent AMI
export RBENV_VERSION="3.1.4"
eval "$(rbenv init -)"

logger "Logging into ECR"
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin https://${ECR_REGISTRY}
